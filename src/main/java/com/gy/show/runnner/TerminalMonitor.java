package com.gy.show.runnner;

import com.gy.show.service.RequirementTaskService;
import com.gy.show.ws.FullViewTsServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TerminalMonitor implements ApplicationRunner {

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Autowired
    private FullViewTsServer server;


    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 1.获取当前时间在任务开始时间和结束时间之间的任务集合

        // 2.根据当前时间来计算任务执行的百分比

        // 3.将任务集合组装后通过websocket推送到前端

    }
}
